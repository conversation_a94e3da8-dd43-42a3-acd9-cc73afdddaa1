import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Href, Link } from 'expo-router';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from '../../../constants/theme';
import { authStyles } from '../styles';

interface AuthFormProps {
  title: string;
  subtitle: string;
  email: string;
  password: string;
  confirmPassword?: string;
  loading: boolean;
  buttonText: string;
  loadingText: string;
  linkText: string;
  linkHref: Href;
  linkButtonText: string;
  showConfirmPassword?: boolean;
  onEmailChange: (email: string) => void;
  onPasswordChange: (password: string) => void;
  onConfirmPasswordChange?: (password: string) => void;
  onSubmit: () => void;
}

export default function AuthForm({
  title,
  subtitle,
  email,
  password,
  confirmPassword,
  loading,
  buttonText,
  loadingText,
  linkText,
  linkHref,
  linkButtonText,
  showConfirmPassword = false,
  onEmailChange,
  onPasswordChange,
  onConfirmPasswordChange,
  onSubmit,
}: AuthFormProps) {
  const [emailFocused, setEmailFocused] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [confirmPasswordFocused, setConfirmPasswordFocused] = useState(false);

  return (
    <SafeAreaView style={authStyles.container}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={authStyles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          {/* Islamic Branding Header */}
          <View style={authStyles.headerSection}>
            <View style={authStyles.brandingContainer}>
              <View style={authStyles.logoContainer}>
                <Text style={authStyles.logoIcon}>🕌</Text>
              </View>
              <Text style={authStyles.brandTitle}>Tabungan Berkah</Text>
              <Text style={authStyles.brandSubtitle}>Tabungan Haji Terpercaya</Text>
              <View style={authStyles.islamicAccent}>
                <Text style={authStyles.arabicText}>بِسْمِ اللَّهِ</Text>
              </View>
            </View>
          </View>

          {/* Form Card */}
          <View style={authStyles.formCard}>
            <Text style={authStyles.title}>{title}</Text>
            <Text style={authStyles.subtitle}>{subtitle}</Text>

            {/* Email Input */}
            <View style={authStyles.inputContainer}>
              <Text style={authStyles.label}>Email</Text>
              <View style={authStyles.inputWrapper}>
                <TextInput
                  style={[
                    authStyles.input,
                    emailFocused && authStyles.inputFocused
                  ]}
                  value={email}
                  onChangeText={onEmailChange}
                  onFocus={() => setEmailFocused(true)}
                  onBlur={() => setEmailFocused(false)}
                  placeholder="Masukkan email Anda"
                  placeholderTextColor={Colors.textTertiary}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            </View>

            {/* Password Input */}
            <View style={authStyles.inputContainer}>
              <Text style={authStyles.label}>Kata Sandi</Text>
              <View style={authStyles.inputWrapper}>
                <TextInput
                  style={[
                    authStyles.input,
                    passwordFocused && authStyles.inputFocused
                  ]}
                  value={password}
                  onChangeText={onPasswordChange}
                  onFocus={() => setPasswordFocused(true)}
                  onBlur={() => setPasswordFocused(false)}
                  placeholder="Masukkan kata sandi Anda"
                  placeholderTextColor={Colors.textTertiary}
                  secureTextEntry
                  autoCapitalize="none"
                />
              </View>
            </View>

            {/* Confirm Password Input */}
            {showConfirmPassword && (
              <View style={authStyles.inputContainer}>
                <Text style={authStyles.label}>Konfirmasi Kata Sandi</Text>
                <View style={authStyles.inputWrapper}>
                  <TextInput
                    style={[
                      authStyles.input,
                      confirmPasswordFocused && authStyles.inputFocused
                    ]}
                    value={confirmPassword}
                    onChangeText={onConfirmPasswordChange}
                    onFocus={() => setConfirmPasswordFocused(true)}
                    onBlur={() => setConfirmPasswordFocused(false)}
                    placeholder="Konfirmasi kata sandi Anda"
                    placeholderTextColor={Colors.textTertiary}
                    secureTextEntry
                    autoCapitalize="none"
                  />
                </View>
              </View>
            )}

            {/* Submit Button */}
            <View style={authStyles.buttonContainer}>
              <TouchableOpacity
                style={[
                  authStyles.button,
                  loading && authStyles.buttonDisabled
                ]}
                onPress={onSubmit}
                disabled={loading}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[Colors.primary, Colors.primaryLight]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={authStyles.buttonGradient}
                >
                  {loading ? (
                    <View style={authStyles.loadingContainer}>
                      <ActivityIndicator size="small" color={Colors.textOnPrimary} />
                      <Text style={authStyles.loadingText}>{loadingText}</Text>
                    </View>
                  ) : (
                    <Text style={authStyles.buttonText}>{buttonText}</Text>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            </View>

            {/* Navigation Link */}
            <View style={authStyles.linkContainer}>
              <Text style={authStyles.linkText}>{linkText} </Text>
              <Link href={linkHref} style={authStyles.link}>
                <Text style={authStyles.linkTextBold}>{linkButtonText}</Text>
              </Link>
            </View>
          </View>

          {/* Security Footer */}
          <View style={authStyles.footerContainer}>
            <View style={authStyles.securityBadge}>
              <Ionicons name="shield-checkmark" size={16} color={Colors.primary} />
              <Text style={authStyles.securityText}>Keamanan Terjamin</Text>
            </View>
            <Text style={authStyles.footerText}>
              Data Anda dilindungi dengan enkripsi tingkat bank
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
