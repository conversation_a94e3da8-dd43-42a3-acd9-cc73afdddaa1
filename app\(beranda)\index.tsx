import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import { SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { AgentCard } from '../../components/AgentCard';
import { GridCol, GridContainer, GridRow, Section } from '../../components/ProfessionalGrid';
import { ProgressCard } from '../../components/ProgressCard';
import { BorderRadius, Colors, Shadows, Spacing } from '../../constants/theme';
import { useAuth } from '../../contexts/AuthContext';



export default function HomeScreen() {
  const { user, signOut } = useAuth();

  // Mock data - in real app, this would come from database
  const savingsData = {
    currentAmount: 2500000,
    targetAmount: 10000000,
    selectedAgent: 'Ibu Sari',
    monthsRemaining: 8
  };

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login');
  };

  const handleProgressCardPress = () => {
    // Navigate to detailed progress view
    console.log('Progress card pressed');
  };

  const handleAgentPress = () => {
    // Navigate to agent contact
    console.log('Agent card pressed');
  };



  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Professional Header with Islamic Branding */}
        <LinearGradient
          colors={[Colors.primary, Colors.primaryDark]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <View style={styles.brandingSection}>
                <View style={styles.logoContainer}>
                  <Text style={styles.logoIcon}>🕌</Text>
                </View>
                <View style={styles.brandingText}>
                  <Text style={styles.appTitle}>Tabungan Berkah</Text>
                  <Text style={styles.headerSubtitle}>Tabungan Haji Terpercaya</Text>
                  <View style={styles.islamicAccent}>
                    <Text style={styles.arabicText}>بِسْمِ اللَّهِ</Text>
                  </View>
                </View>
              </View>
              <View style={styles.headerActions}>
                <TouchableOpacity style={styles.notificationButton}>
                  <Ionicons name="notifications-outline" size={20} color="#fff" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
                  <Ionicons name="log-out-outline" size={20} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </LinearGradient>

        {/* Professional Grid Layout */}
        <GridContainer>
          {/* Compact Welcome Section */}
          <Section spacing="sm">
            <View style={styles.compactWelcomeCard}>
              <View style={styles.compactWelcomeContent}>
                <View style={styles.compactAvatarSection}>
                  <Ionicons name="person-circle" size={32} color={Colors.primary} />
                </View>
                <View style={styles.compactWelcomeInfo}>
                  <Text style={styles.compactWelcomeText}>Selamat Datang!</Text>
                  <Text style={styles.compactUserEmail}>{user?.email}</Text>
                </View>
                <View style={styles.compactStatusIndicator}>
                  <View style={styles.compactStatusDot} />
                  <Text style={styles.compactStatusText}>Aktif</Text>
                </View>
              </View>
            </View>
          </Section>

          {/* Compact Progress Section with Time */}
          <Section spacing="md">
            <ProgressCard
              currentAmount={savingsData.currentAmount}
              targetAmount={savingsData.targetAmount}
              monthsRemaining={savingsData.monthsRemaining}
              hajjSeason="1446 H"
              onPress={handleProgressCardPress}
            />
          </Section>

          {/* Compact Quick Actions Grid */}
          <Section spacing="sm">
            <GridRow gap={Spacing.xs}>
              <GridCol flex={1}>
                <TouchableOpacity style={styles.compactActionCard} activeOpacity={0.8}>
                  <Ionicons name="add-circle" size={20} color={Colors.primary} />
                  <Text style={styles.compactActionText}>Setor</Text>
                </TouchableOpacity>
              </GridCol>
              <GridCol flex={1}>
                <TouchableOpacity style={styles.compactActionCard} activeOpacity={0.8}>
                  <Ionicons name="document-text" size={20} color={Colors.primary} />
                  <Text style={styles.compactActionText}>Riwayat</Text>
                </TouchableOpacity>
              </GridCol>
              <GridCol flex={1}>
                <TouchableOpacity style={styles.compactActionCard} activeOpacity={0.8}>
                  <Ionicons name="calculator" size={20} color={Colors.primary} />
                  <Text style={styles.compactActionText}>Simulasi</Text>
                </TouchableOpacity>
              </GridCol>
              <GridCol flex={1}>
                <TouchableOpacity style={styles.compactActionCard} activeOpacity={0.8}>
                  <Ionicons name="settings" size={20} color={Colors.primary} />
                  <Text style={styles.compactActionText}>Lainnya</Text>
                </TouchableOpacity>
              </GridCol>
            </GridRow>
          </Section>

          {/* Compact Agent & Motivation Grid */}
          <Section spacing="sm">
            <GridRow gap={Spacing.sm}>
              <GridCol flex={2}>
                <View style={styles.compactSectionHeader}>
                  <Text style={styles.compactSectionTitle}>Agent Haji ☪</Text>
                </View>
                <AgentCard
                  agentName={savingsData.selectedAgent}
                  agentTitle="Konsultan Haji"
                  onPress={handleAgentPress}
                  onChatPress={() => console.log('Chat pressed')}
                  onCallPress={() => console.log('Call pressed')}
                />
              </GridCol>
            </GridRow>
          </Section>

          {/* Compact Islamic Motivation */}
          <Section spacing="sm">
            <View style={styles.compactMotivationCard}>
              <View style={styles.compactMotivationHeader}>
                <Text style={styles.motivationIcon}>🤲</Text>
                <Text style={styles.compactMotivationTitle}>Doa Hari Ini</Text>
              </View>
              <Text style={styles.compactMotivationText}>
                "Dan berbekal-lah, sesungguhnya sebaik-baik bekal adalah takwa"
              </Text>
              <Text style={styles.compactMotivationSource}>- Al-Baqarah: 197</Text>
            </View>
          </Section>
        </GridContainer>


      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollContent: {
    paddingBottom: 100, // Space for navigation bar
  },

  headerGradient: {
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.container.horizontal,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    marginBottom: Spacing.lg,
  },

  header: {
    minHeight: 80,
  },

  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 60,
  },

  brandingSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  logoContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  logoIcon: {
    fontSize: 24,
  },

  brandingText: {
    flex: 1,
  },

  appTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 2,
    letterSpacing: 0.5,
  },

  headerSubtitle: {
    fontSize: 13,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.85)',
    letterSpacing: 0.3,
    marginBottom: 4,
  },

  // Islamic Branding
  islamicAccent: {
    alignItems: 'flex-start',
  },

  arabicText: {
    fontSize: 11,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: 'System', // Will use system Arabic font
    letterSpacing: 1,
  },

  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },

  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  signOutButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  welcomeCard: {
    marginHorizontal: Spacing.container.horizontal,
    marginBottom: Spacing.lg,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.card,
    padding: Spacing.card.padding,
    ...Shadows.card,
  },

  welcomeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },

  avatarSection: {
    alignItems: 'center',
  },

  avatarContainer: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: Colors.accentLight,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.primary,
  },

  welcomeInfo: {
    flex: 1,
  },

  welcomeText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 4,
  },

  userEmail: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textSecondary,
    marginBottom: 6,
  },

  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },

  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.success,
  },

  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.success,
  },

  // Compact Welcome Card
  compactWelcomeCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.xs,
    padding: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.accentMuted,
  },

  compactWelcomeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },

  compactAvatarSection: {
    alignItems: 'center',
  },

  compactWelcomeInfo: {
    flex: 1,
  },

  compactWelcomeText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 2,
  },

  compactUserEmail: {
    fontSize: 11,
    fontWeight: '400',
    color: Colors.textSecondary,
  },

  compactStatusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  compactStatusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.success,
  },

  compactStatusText: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.success,
  },

  // Professional Grid Layout
  gridContainer: {
    marginHorizontal: Spacing.container.horizontal,
    marginBottom: Spacing.lg,
  },

  gridRow: {
    flexDirection: 'row',
    gap: Spacing.layout.columnGap,
    marginBottom: Spacing.md,
  },

  timeRemainingSection: {
    flex: 1,
    alignItems: 'flex-start',
  },

  timeRemainingContainer: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
    alignItems: 'flex-start',
  },

  agentSection: {
    marginBottom: Spacing.lg,
  },

  // Section Styling
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    letterSpacing: 0.3,
  },

  // Islamic Decorative Elements
  islamicDecoration: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  decorativeText: {
    fontSize: 16,
    color: Colors.gold,
  },

  // Islamic Motivation Card
  motivationCard: {
    backgroundColor: Colors.goldLight,
    borderRadius: BorderRadius.card,
    padding: Spacing.card.padding,
    borderLeftWidth: 4,
    borderLeftColor: Colors.gold,
    ...Shadows.card,
  },

  motivationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: Spacing.sm,
  },

  motivationIcon: {
    fontSize: 20,
  },

  motivationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },

  motivationText: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textPrimary,
    lineHeight: 20,
    marginBottom: Spacing.xs,
    fontStyle: 'italic',
  },

  motivationSource: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textSecondary,
    textAlign: 'right',
  },

  // Compact Section Headers
  compactSectionHeader: {
    marginBottom: Spacing.xs,
  },

  compactSectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },

  // Compact Motivation Card
  compactMotivationCard: {
    backgroundColor: Colors.goldLight,
    borderRadius: BorderRadius.xs,
    padding: Spacing.sm,
    borderLeftWidth: 3,
    borderLeftColor: Colors.gold,
  },

  compactMotivationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 6,
  },

  compactMotivationTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.textPrimary,
  },

  compactMotivationText: {
    fontSize: 11,
    fontWeight: '400',
    color: Colors.textPrimary,
    lineHeight: 16,
    marginBottom: 4,
    fontStyle: 'italic',
  },

  compactMotivationSource: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.textSecondary,
    textAlign: 'right',
  },

  // Compact Quick Actions (BRImo style)
  compactActionCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.xs,
    padding: Spacing.sm,
    alignItems: 'center',
    gap: 4,
    borderWidth: 1,
    borderColor: Colors.accentMuted,
    minHeight: 60,
    ...Shadows.button,
  },

  compactActionText: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.textPrimary,
    textAlign: 'center',
  },

  // Legacy Quick Actions
  quickActionCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.sm,
    padding: Spacing.md,
    alignItems: 'center',
    gap: Spacing.xs,
    borderWidth: 1,
    borderColor: Colors.accentMuted,
    minHeight: 80,
    ...Shadows.button,
  },

  quickActionText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textPrimary,
    textAlign: 'center',
  },
});