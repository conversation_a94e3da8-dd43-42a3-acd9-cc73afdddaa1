import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import AuthForm from './components/AuthForm';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { signIn } = useAuth();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Kesalahan', 'Harap isi semua kolom');
      return;
    }

    setLoading(true);
    const { error } = await signIn(email, password);

    if (error) {
      Alert.alert('Kesalahan Masuk', error.message);
    } else {
      // Navigation will be handled by the auth context
      router.replace('/(beranda)');
    }
    setLoading(false);
  };

  return (
    <AuthForm
      title="Selamat Datang"
      subtitle="Masuk ke akun tabungan haji <PERSON>"
      email={email}
      password={password}
      loading={loading}
      buttonText="Masuk"
      loadingText="Sedang Masuk..."
      linkText="Belum punya akun?"
      linkHref="/(auth)/register"
      linkButtonText="Daftar Sekarang"
      onEmailChange={setEmail}
      onPasswordChange={setPassword}
      onSubmit={handleLogin}
    />
  );
}


