import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { BorderRadius, Colors, Shadows, Spacing } from '../constants/theme';


interface AgentCardProps {
  agentName: string;
  agentTitle?: string;
  rating?: number;
  responseTime?: string;
  onPress?: () => void;
  onCallPress?: () => void;
  onChatPress?: () => void;
  style?: any;
}

export const AgentCard: React.FC<AgentCardProps> = ({
  agentName,
  agentTitle = "Agent Terpercaya",
  rating = 4.9,
  responseTime = "< 5 menit",
  onPress,
  onCallPress,
  onChatPress,
  style
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.9}
      accessibilityRole="button"
      accessibilityLabel={`Agent ${agentName}, siap membantu Anda`}
      accessibilityHint="Ketuk untuk menghubungi agent melalui chat"
      accessible={true}
    >
      <View style={styles.compactCard}>
        {/* Compact Header */}
        <View style={styles.compactHeader}>
          <View style={styles.headerLeft}>
            <Ionicons name="headset" size={16} color={Colors.primary} />
            <Text style={styles.cardTitle}>Agent Haji</Text>
          </View>
          <View style={styles.ratingBadge}>
            <Ionicons name="star" size={12} color={Colors.gold} />
            <Text style={styles.ratingText}>{rating}</Text>
          </View>
        </View>

        {/* Compact Content */}
        <View style={styles.compactContent}>
          {/* Avatar */}
          <View style={styles.compactAvatar}>
            <LinearGradient
              colors={[Colors.primary, Colors.primaryLight]}
              style={styles.avatarGradient}
            >
              <Ionicons name="person" size={20} color="#fff" />
            </LinearGradient>
            <View style={styles.onlineDot} />
          </View>

          {/* Info */}
          <View style={styles.compactInfo}>
            <Text style={styles.agentName}>{agentName}</Text>
            <Text style={styles.agentTitle}>{agentTitle}</Text>
            <Text style={styles.responseText}>Respon {responseTime}</Text>
          </View>

          {/* Actions */}
          <View style={styles.compactActions}>
            <TouchableOpacity
              style={styles.compactButton}
              onPress={onChatPress}
              activeOpacity={0.8}
            >
              <Ionicons name="chatbubble" size={16} color={Colors.primary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.compactButton, styles.primaryCompactButton]}
              onPress={onCallPress}
              activeOpacity={0.8}
            >
              <Ionicons name="call" size={16} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.sm,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
    ...Shadows.button,
  },

  // Compact Card Style
  compactCard: {
    backgroundColor: Colors.surface,
    padding: Spacing.md,
    borderRadius: BorderRadius.sm,
    borderWidth: 1,
    borderColor: Colors.accentMuted,
  },

  // Compact Header
  compactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },

  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },

  cardTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.textSecondary,
  },

  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
    backgroundColor: Colors.goldLight,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },

  ratingText: {
    fontSize: 11,
    fontWeight: '600',
    color: Colors.textPrimary,
  },

  // Compact Content
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },

  // Compact Avatar
  compactAvatar: {
    position: 'relative',
  },

  avatarGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  onlineDot: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.success,
    borderWidth: 2,
    borderColor: Colors.surface,
  },

  // Compact Info
  compactInfo: {
    flex: 1,
    gap: 2,
  },

  agentName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },

  agentTitle: {
    fontSize: 11,
    fontWeight: '500',
    color: Colors.textSecondary,
  },

  responseText: {
    fontSize: 10,
    fontWeight: '400',
    color: Colors.success,
  },

  // Compact Actions
  compactActions: {
    flexDirection: 'row',
    gap: 6,
  },

  compactButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.accentLight,
  },

  primaryCompactButton: {
    backgroundColor: Colors.primary,
  },
});

export default AgentCard;
