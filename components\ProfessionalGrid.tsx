import React from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { Spacing } from '../constants/theme';

interface GridContainerProps {
  children: React.ReactNode;
  style?: ViewStyle;
  spacing?: number;
}

interface GridRowProps {
  children: React.ReactNode;
  style?: ViewStyle;
  gap?: number;
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
}

interface GridColProps {
  children: React.ReactNode;
  style?: ViewStyle;
  flex?: number;
  width?: number | string;
}

// Professional Grid Container
export const GridContainer: React.FC<GridContainerProps> = ({
  children,
  style,
  spacing = Spacing.container.horizontal
}) => {
  return (
    <View style={[styles.container, { paddingHorizontal: spacing }, style]}>
      {children}
    </View>
  );
};

// Professional Grid Row
export const GridRow: React.FC<GridRowProps> = ({
  children,
  style,
  gap = Spacing.layout.columnGap,
  align = 'stretch'
}) => {
  return (
    <View style={[
      styles.row, 
      { gap, alignItems: align }, 
      style
    ]}>
      {children}
    </View>
  );
};

// Professional Grid Column
export const GridCol: React.FC<GridColProps> = ({
  children,
  style,
  flex,
  width
}) => {
  const colStyle: ViewStyle = {
    ...(flex !== undefined && { flex }),
    ...(width !== undefined && { width }),
  };

  return (
    <View style={[styles.col, colStyle, style]}>
      {children}
    </View>
  );
};

// Professional Section Container
interface SectionProps {
  children: React.ReactNode;
  style?: ViewStyle;
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
}

export const Section: React.FC<SectionProps> = ({
  children,
  style,
  spacing = 'md'
}) => {
  const spacingValue = {
    sm: Spacing.md,
    md: Spacing.lg,
    lg: Spacing.xl,
    xl: Spacing.xxl
  }[spacing];

  return (
    <View style={[styles.section, { marginBottom: spacingValue }, style]}>
      {children}
    </View>
  );
};

// Professional Card Grid (for multiple cards in a row)
interface CardGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3;
  gap?: number;
  style?: ViewStyle;
}

export const CardGrid: React.FC<CardGridProps> = ({
  children,
  columns = 2,
  gap = Spacing.layout.columnGap,
  style
}) => {
  return (
    <View style={[styles.cardGrid, { gap }, style]}>
      {React.Children.map(children, (child, index) => (
        <View style={[
          styles.cardGridItem,
          { flex: 1 / columns }
        ]}>
          {child}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },

  row: {
    flexDirection: 'row',
    width: '100%',
  },

  col: {
    // Base column styles
  },

  section: {
    width: '100%',
  },

  cardGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
  },

  cardGridItem: {
    minWidth: 0, // Prevents flex items from overflowing
  },
});

export default {
  GridContainer,
  GridRow,
  GridCol,
  Section,
  CardGrid,
};
