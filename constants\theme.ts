// TabunganBerkah - Tabungan Haji Design System
// Based on JSON specification for professional, elderly-friendly UI

export const Colors = {
  // Professional Blue Palette - Enhanced for Islamic Banking
  primary: '#0055AA',
  primaryLight: '#3D7BC6',
  primaryDark: '#003366',
  secondary: '#4479CA',

  // Professional Neutrals
  background: '#FFFFFF',
  backgroundSecondary: '#F8FAFC',
  surface: '#FFFFFF',
  surfaceElevated: '#F5F8FC',

  // Text Hierarchy
  textPrimary: '#1A202C',
  textSecondary: '#4A5568',
  textTertiary: '#718096',
  textOnPrimary: '#FFFFFF',

  // Professional Accents
  accent: '#0055AA',
  accentLight: '#E6F3FF',
  accentMuted: '#B3D9FF',

  // Status Colors
  success: '#10B981',
  successLight: '#D1FAE5',
  warning: '#F59E0B',
  warningLight: '#FEF3C7',
  error: '#EF4444',
  errorLight: '#FEE2E2',

  // Islamic/Hajj Theme Colors
  gold: '#D4AF37',
  goldLight: '#F7F0D7',
  emerald: '#059669',
  emeraldLight: '#D1FAE5',

  // Professional Gradients
  gradients: {
    primary: ['#0055AA', '#3D7BC6'],
    header: ['#0055AA', '#003366'],
    card: ['#F8FAFC', '#FFFFFF'],
    progress: ['#0055AA', '#4479CA'],
    islamic: ['#0055AA', '#D4AF37'],
  },

  // Legacy support
  base: '#0055AA',
  light: '#AED6F1',
  medium: '#4479CA',
  dark: '#003366',
  neutral_light: '#F5F8FC',
  text_primary: '#1A202C',
  text_secondary: '#4A5568',
};

export const Typography = {
  // Font families
  fonts: {
    primary: 'Inter_400Regular', // Sans-serif modern
    primaryMedium: 'Inter_500Medium',
    primarySemiBold: 'Inter_600SemiBold',
    primaryBold: 'Inter_700Bold',
    secondary: 'PlayfairDisplay_400Regular', // Serif ringan
    secondaryBold: 'PlayfairDisplay_700Bold',
    fallback: 'System'
  },
  
  // Font sizes - elderly-friendly scale
  sizes: {
    h1: 24,
    h2: 20,
    body: 16,
    caption: 14,
    small: 12
  },
  
  // Font weights
  weights: {
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  },
  
  // Line heights for readability
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6
  }
};

export const Spacing = {
  // Professional 8px grid system
  gridUnit: 8,

  // Micro spacing
  xs: 4,    // 0.5 * grid - tight spacing
  sm: 8,    // 1 * grid - small gaps
  md: 16,   // 2 * grid - standard spacing
  lg: 24,   // 3 * grid - section spacing
  xl: 32,   // 4 * grid - large sections
  xxl: 48,  // 6 * grid - major sections
  xxxl: 64, // 8 * grid - page sections

  // Professional Layout Spacing
  container: {
    horizontal: 20,  // Professional edge margins
    vertical: 24,    // Vertical rhythm
    section: 32,     // Between major sections
  },

  // Component-specific spacing
  card: {
    padding: 20,
    margin: 16,
    gap: 12,
  },

  button: {
    padding: 16,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },

  input: {
    padding: 16,
    margin: 8,
  },

  // Grid system for professional layouts
  layout: {
    gutter: 16,      // Space between grid items
    margin: 20,      // Grid container margins
    columnGap: 12,   // Column spacing
    rowGap: 16,      // Row spacing
  },

  // Legacy support
  grid: 8,
  margins: 16,
  padding: {
    card: 20,
    button: 16,
    input: 12
  }
};

export const BorderRadius = {
  // Rounded values for organic shapes
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,  // Standard card radius
  xl: 24,
  pill: 999, // For pill-rounded elements
  
  // Component-specific
  card: 16,
  button: 12,
  input: 8
};

export const Shadows = {
  // Soft shadows for depth
  card: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  
  button: {
    shadowColor: '#0055AA',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  
  floating: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
  }
};

export const Layout = {
  // Professional Touch Targets
  touchTarget: {
    minHeight: 48,
    minWidth: 48,
    recommended: 56, // Larger for elderly-friendly design
  },

  // Professional Container System
  container: {
    maxWidth: 400,
    padding: 20,
    margin: 16,
  },

  // Professional Grid System
  grid: {
    columns: 12,
    maxWidth: 400,
    gutter: 16,
    margin: 20,
  },

  // Professional Card Layouts
  card: {
    // Standard card
    standard: {
      minHeight: 80,
      padding: 20,
      borderRadius: 16,
    },

    // Compact card for grid layouts
    compact: {
      minHeight: 60,
      padding: 16,
      borderRadius: 12,
    },

    // Feature card (larger)
    feature: {
      minHeight: 120,
      padding: 24,
      borderRadius: 20,
    },

    // Progress card (custom)
    progress: {
      minHeight: 140,
      padding: 24,
      borderRadius: 20,
    }
  },

  // Professional Button System
  button: {
    primary: {
      height: 48,
      paddingHorizontal: 24,
      borderRadius: 12,
    },

    secondary: {
      height: 44,
      paddingHorizontal: 20,
      borderRadius: 10,
    },

    compact: {
      height: 36,
      paddingHorizontal: 16,
      borderRadius: 8,
    }
  },

  // Professional Header Layout
  header: {
    height: 120,
    paddingTop: 24,
    paddingBottom: 32,
    paddingHorizontal: 20,
  },

  // Legacy support
  minHeight: 80,
  padding: 20
};

export const Animation = {
  // Smooth, natural animations
  duration: {
    fast: 200,
    normal: 300,
    slow: 500
  },
  
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out'
  }
};

// Professional Component Theme Configurations
export const ComponentThemes = {
  // Professional Header
  header: {
    height: 120,
    borderRadius: 24,
    padding: 20,
    gradient: Colors.gradients.header,
    shadow: Shadows.card,
  },

  // Professional Welcome Card
  welcomeCard: {
    borderRadius: 16,
    backgroundColor: Colors.surface,
    padding: 20,
    margin: 16,
    shadow: Shadows.card,
    minHeight: 80,
  },

  // Professional Progress Card
  progressCard: {
    borderRadius: 20,
    backgroundColor: Colors.accentLight,
    padding: 24,
    margin: 16,
    minHeight: 140,
    shadow: Shadows.card,
    gradient: Colors.gradients.card,
  },

  // Professional Time Card
  timeRemainingCard: {
    borderRadius: 12,
    backgroundColor: Colors.surfaceElevated,
    padding: 16,
    minHeight: 60,
    shadow: Shadows.button,
  },

  // Professional Agent Card
  agentCard: {
    borderRadius: 16,
    backgroundColor: Colors.surface,
    padding: 20,
    margin: 16,
    minHeight: 80,
    shadow: Shadows.card,
  },

  // Professional Navigation
  navigationBar: {
    borderRadius: 24,
    backgroundColor: Colors.surface,
    height: 80,
    paddingBottom: 10,
    paddingTop: 10,
    shadow: Shadows.floating,
  },

  // Professional Grid Cards
  gridCard: {
    borderRadius: 12,
    backgroundColor: Colors.surface,
    padding: 16,
    minHeight: 60,
    shadow: Shadows.button,
  },

  // Professional Feature Cards
  featureCard: {
    borderRadius: 20,
    backgroundColor: Colors.surface,
    padding: 24,
    minHeight: 120,
    shadow: Shadows.card,
  },

  // Legacy support
  card: {
    borderRadius: BorderRadius.card,
    backgroundColor: Colors.surface,
    padding: Spacing.padding.card,
    shadow: Shadows.card
  }
};

// Accessibility configurations
export const Accessibility = {
  contrastRatio: 'WCAG AA+',
  touchSize: Layout.touchTarget,
  screenReader: true,
  
  // High contrast colors for better visibility
  highContrast: {
    text: Colors.text_primary,
    background: Colors.background,
    accent: Colors.base
  }
};

export default {
  Colors,
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  Layout,
  ComponentThemes,
  Accessibility
};
