import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Shadows, Spacing } from '../../constants/theme';

export const authStyles = StyleSheet.create({
  // Main container with professional background
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSecondary,
  },

  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xl,
  },

  // Header section with Islamic branding
  headerSection: {
    alignItems: 'center',
    marginBottom: Spacing.xl * 2,
    paddingTop: Spacing.xl,
  },

  // Islamic branding elements
  brandingContainer: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },

  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.md,
    ...Shadows.card,
    transform: [{ rotate: '2deg' }],
  },

  logoIcon: {
    fontSize: 36,
    color: '#fff',
  },

  brandTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 4,
    textAlign: 'center',
  },

  brandSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 8,
  },

  islamicAccent: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    backgroundColor: Colors.accentLight,
    borderRadius: BorderRadius.pill,
  },

  arabicText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },

  // Form container with card design
  formCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
    marginHorizontal: Spacing.xs,
    ...Shadows.card,
    transform: [{ rotate: '-0.5deg' }],
  },

  // Title and subtitle
  title: {
    fontSize: 26,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: Colors.textPrimary,
  },

  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    color: Colors.textSecondary,
    lineHeight: 22,
  },

  // Input styling to match beranda
  inputContainer: {
    marginBottom: Spacing.lg,
  },

  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: Spacing.sm,
    color: Colors.textPrimary,
  },

  inputWrapper: {
    position: 'relative',
  },

  input: {
    borderWidth: 2,
    borderColor: Colors.accentMuted,
    borderRadius: BorderRadius.input,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.surface,
    color: Colors.textPrimary,
    minHeight: 48, // Elderly-friendly touch target
  },

  inputFocused: {
    borderColor: Colors.primary,
    backgroundColor: Colors.accentLight,
  },

  inputError: {
    borderColor: Colors.error,
    backgroundColor: Colors.errorLight,
  },

  // Button styling to match beranda
  buttonContainer: {
    marginTop: Spacing.lg,
    marginBottom: Spacing.md,
  },

  button: {
    borderRadius: BorderRadius.button,
    overflow: 'hidden',
    ...Shadows.button,
    transform: [{ rotate: '0.5deg' }],
  },

  buttonGradient: {
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48, // Elderly-friendly touch target
  },

  buttonDisabled: {
    opacity: 0.6,
    transform: [{ rotate: '0deg' }],
  },

  buttonText: {
    color: Colors.textOnPrimary,
    fontSize: 18,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },

  // Link styling
  linkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.lg,
    paddingVertical: Spacing.md,
  },

  linkText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },

  link: {
    marginLeft: 4,
  },

  linkTextBold: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: 'bold',
  },

  // Loading state
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  loadingText: {
    color: Colors.textOnPrimary,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: Spacing.sm,
  },

  // Professional footer
  footerContainer: {
    alignItems: 'center',
    marginTop: Spacing.xl,
    paddingTop: Spacing.lg,
  },

  footerText: {
    fontSize: 14,
    color: Colors.textTertiary,
    textAlign: 'center',
    lineHeight: 20,
  },

  // Security badge
  securityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.accentLight,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.pill,
    marginTop: Spacing.md,
  },

  securityText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
    marginLeft: Spacing.xs,
  },
});
