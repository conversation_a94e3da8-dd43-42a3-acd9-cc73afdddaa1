import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { BorderRadius, Colors, Spacing } from '../constants/theme';


interface TimeRemainingCardProps {
  monthsRemaining: number;
  targetDate?: string;
  hajjSeason?: string;
  style?: any;
}

export const TimeRemainingCard: React.FC<TimeRemainingCardProps> = ({
  monthsRemaining,
  targetDate = "2025",
  hajjSeason = "1446 H",
  style
}) => {
  return (
    <View
      style={[styles.container, style]}
      accessibilityRole="text"
      accessibilityLabel={`Waktu tersisa ${monthsRemaining} bulan lagi untuk mencapai target tabungan haji ${hajjSeason}`}
      accessible={true}
    >
      {/* Professional Header */}
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons
            name="calendar-outline"
            size={20}
            color={Colors.primary}
          />
        </View>
        <Text style={styles.headerTitle}>Waktu Tersisa</Text>
      </View>

      {/* Main Content */}
      <View style={styles.content}>
        <View style={styles.timeDisplay}>
          <Text style={styles.valueText}>{monthsRemaining}</Text>
          <Text style={styles.labelText}>Bulan Lagi</Text>
        </View>

        <View style={styles.targetInfo}>
          <View style={styles.targetRow}>
            <Ionicons name="flag" size={14} color={Colors.textSecondary} />
            <Text style={styles.targetText}>Target {targetDate}</Text>
          </View>
          <View style={styles.targetRow}>
            <Ionicons name="moon" size={14} color={Colors.gold} />
            <Text style={styles.hajjText}>Haji {hajjSeason}</Text>
          </View>
        </View>
      </View>

      {/* Progress Indicator */}
      <View style={styles.progressSection}>
        <View style={styles.progressDots}>
          {[...Array(12)].map((_, index) => (
            <View
              key={index}
              style={[
                styles.progressDot,
                index < (12 - monthsRemaining) ? styles.progressDotActive : styles.progressDotInactive
              ]}
            />
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.card,
    padding: Spacing.card.padding,
    minHeight: 120,
    borderWidth: 1,
    borderColor: Colors.accentMuted,
  },

  // Professional Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    marginBottom: Spacing.sm,
  },

  iconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.accentLight,
    alignItems: 'center',
    justifyContent: 'center',
  },

  headerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },

  // Main Content
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },

  timeDisplay: {
    alignItems: 'center',
  },

  valueText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary,
  },

  labelText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textSecondary,
    marginTop: 2,
  },

  // Target Information
  targetInfo: {
    alignItems: 'flex-end',
    gap: 4,
  },

  targetRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  targetText: {
    fontSize: 11,
    fontWeight: '500',
    color: Colors.textSecondary,
  },

  hajjText: {
    fontSize: 11,
    fontWeight: '600',
    color: Colors.gold,
  },

  // Progress Section
  progressSection: {
    marginTop: Spacing.xs,
  },

  progressDots: {
    flexDirection: 'row',
    gap: 4,
    justifyContent: 'center',
  },

  progressDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },

  progressDotActive: {
    backgroundColor: Colors.primary,
  },

  progressDotInactive: {
    backgroundColor: Colors.accentMuted,
  },
});

export default TimeRemainingCard;
