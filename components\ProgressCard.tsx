import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import { BorderRadius, Colors, Shadows, Spacing } from '../constants/theme';
import { formatCurrency } from '../utils/designSystem';

interface ProgressCardProps {
  currentAmount: number;
  targetAmount: number;
  monthsRemaining: number;
  hajjSeason?: string;
  onPress?: () => void;
  style?: any;
}

export const ProgressCard: React.FC<ProgressCardProps> = ({
  currentAmount,
  targetAmount,
  monthsRemaining,
  hajjSeason = "1446 H",
  onPress,
  style
}) => {
  const progressPercentage = Math.round((currentAmount / targetAmount) * 100);
  const remainingAmount = targetAmount - currentAmount;

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.95}
      accessibilityRole="button"
      accessibilityLabel={`Progress tabungan ${progressPercentage} persen. Terkumpul ${formatCurrency(currentAmount)} dari target ${formatCurrency(targetAmount)}`}
      accessibilityHint="Ketuk untuk melihat detail progress dan riwayat tabungan"
      accessible={true}
    >
      <LinearGradient
        colors={[Colors.primary, Colors.primaryLight]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        {/* Compact BRImo-style Header */}
        <View style={styles.compactHeader}>
          <View style={styles.headerInfo}>
            <Text style={styles.cardTitle}>Tabungan Haji</Text>
            <Text style={styles.hajjSeason}>Musim {hajjSeason}</Text>
          </View>
          <View style={styles.timeRemaining}>
            <Text style={styles.timeValue}>{monthsRemaining}</Text>
            <Text style={styles.timeLabel}>Bulan Lagi</Text>
          </View>
        </View>

        {/* Compact Main Content */}
        <View style={styles.compactContent}>
          {/* Left: Progress Circle */}
          <View style={styles.progressSection}>
            <AnimatedCircularProgress
              size={80}
              width={6}
              fill={progressPercentage}
              tintColor="#fff"
              backgroundColor="rgba(255,255,255,0.3)"
              rotation={0}
              lineCap="round"
              duration={1000}
            >
              {() => (
                <View style={styles.chartCenter}>
                  <Text style={styles.percentageText}>{progressPercentage}%</Text>
                </View>
              )}
            </AnimatedCircularProgress>
          </View>

          {/* Right: Amount Info */}
          <View style={styles.amountSection}>
            <View style={styles.amountRow}>
              <Text style={styles.amountLabel}>Terkumpul</Text>
              <Text style={styles.currentAmount}>
                {formatCurrency(currentAmount)}
              </Text>
            </View>
            <View style={styles.amountRow}>
              <Text style={styles.amountLabel}>Target</Text>
              <Text style={styles.targetAmount}>
                {formatCurrency(targetAmount)}
              </Text>
            </View>
            <View style={styles.amountRow}>
              <Text style={styles.amountLabel}>Sisa</Text>
              <Text style={styles.remainingAmount}>
                {formatCurrency(remainingAmount)}
              </Text>
            </View>
          </View>
        </View>

        {/* Compact Progress Bar */}
        <View style={styles.progressBarSection}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${Math.min(progressPercentage, 100)}%` }
              ]}
            />
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: Spacing.container.horizontal,
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
  },

  gradient: {
    padding: Spacing.md,
  },

  // Compact BRImo-style Header
  compactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },

  headerInfo: {
    flex: 1,
  },

  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 2,
  },

  hajjSeason: {
    fontSize: 12,
    fontWeight: '500',
    color: 'rgba(255,255,255,0.8)',
  },

  timeRemaining: {
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },

  timeValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },

  timeLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: 'rgba(255,255,255,0.8)',
  },

  // Compact Main Content
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.lg,
    marginBottom: Spacing.md,
  },

  progressSection: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  chartCenter: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  percentageText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },

  // Compact Amount Section
  amountSection: {
    flex: 1,
    gap: 6,
  },

  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  amountLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: 'rgba(255,255,255,0.8)',
  },

  currentAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
  },

  targetAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.9)',
  },

  remainingAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.9)',
  },

  // Compact Progress Bar
  progressBarSection: {
    marginTop: Spacing.xs,
  },

  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    overflow: 'hidden',
  },

  progressFill: {
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 2,
  },
});

export default ProgressCard;
