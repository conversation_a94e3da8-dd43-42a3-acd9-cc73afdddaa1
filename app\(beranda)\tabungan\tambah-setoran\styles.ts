import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Spacing } from '../../../../constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  
  keyboardView: {
    flex: 1,
  },
  
  scrollContent: {
    paddingBottom: 100, // Space for navigation bar
  },
  
  headerGradient: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    marginBottom: Spacing.lg,
  },
  
  header: {
    alignItems: 'center',
  },
  
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textAlign: 'center',
  },
  
  headerSubtitle: {
    fontSize: 16,
    color: Colors.light,
    opacity: 0.9,
    textAlign: 'center',
  },
  
  currencyDisplay: {
    backgroundColor: Colors.neutral_light,
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
    marginTop: -Spacing.sm,
    marginBottom: Spacing.sm,
  },
  
  currencyText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.base,
    textAlign: 'center',
  },
  
  actionContainer: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  
  infoContent: {
    flexDirection: 'row',
    gap: Spacing.md,
    alignItems: 'flex-start',
  },
  
  infoText: {
    flex: 1,
    fontSize: 14,
    color: Colors.text_primary,
    lineHeight: 20,
  },
});
